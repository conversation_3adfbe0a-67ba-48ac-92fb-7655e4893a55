"use client"
import Image from "next/image"
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel"

export default function SpecialistsSection() {

  const specialists = [
    {
      name: "BS. Dương Công Đ<PERSON>nh",
      desc:"Implant/Thẩm mỹ sứ",
      image: "/doctor/bs-01.jpg",
    },
    {
      name: "BS. Trần Viết Huyền",
      desc:"Niềng răng, chỉnh nha",
      image: "/doctor/bs-02.jpg",
    },
    {
      name: "YS: <PERSON>uyễn Thị Thuở",
      desc:"Trẻ em & tổng quát",
      image: "/doctor/bs-03.jpg",
    },
  ]

  return (
    <section className="pb-12 bg-white" id="doctor">
      <div className="container mx-auto px-4 max-w-[1124px]">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-800 mb-4"><PERSON><PERSON><PERSON> ngũ bác sĩ tại SGI Dental</h2>
          <div className="flex items-center justify-center mb-6">
            <div className="h-px bg-teal-500 w-16"></div>
            <div className="w-3 h-3 bg-teal-500 rounded-full mx-3 flex items-center justify-center">
              <div className="w-1 h-1 bg-white rounded-full"></div>
            </div>
            <div className="h-px bg-teal-500 w-16"></div>
          </div>
        </div>
        <div className="relative group">
          <Carousel
            opts={{ loop: true }}
            className="w-full mx-auto"
          >
            <CarouselContent>
              {specialists.map((doctor, index) => (
                <CarouselItem
                  key={index}
                  className="md:basis-1/3"
                >
                  <div className="text-center group">
                    <div className="relative mb-6 rounded-lg">
                      <Image
                        src={doctor.image}
                        alt={doctor.name}
                        width={420}
                        height={420}
                        className="object-conver"
                      />
                    </div>
                    <h3 className="text-xl font-bold text-gray-800 mb-2">{doctor.name}</h3>
                    <p className="text-gray-800 mb-2">{doctor.desc}</p>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
          </Carousel>
        </div>
      </div>
    </section>
  )
}