"use client"

import { useState } from "react"
import Image from "next/image"

export default function BeforeAfterSection() {
  const [sliderPosition, setSliderPosition] = useState(50)

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4 max-w-[1124px]">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-800 mb-4">Before and after procedures</h2>
          <div className="flex items-center justify-center mb-6">
            <div className="h-px bg-teal-500 w-16"></div>
            <div className="w-3 h-3 bg-teal-500 rounded-full mx-3 flex items-center justify-center">
              <div className="w-1 h-1 bg-white rounded-full"></div>
            </div>
            <div className="h-px bg-teal-500 w-16"></div>
          </div>
          <div className="max-w-2xl mx-auto">
            <p className="text-gray-600 mb-2">
              Lorem Ipsum is simply dummy text of the printing and typesetting industry.
            </p>
            <p className="text-gray-600">Lorem Ipsum the industry's standard dummy text.</p>
          </div>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="relative overflow-hidden rounded-lg shadow-lg">
            <div className="relative w-full h-96">
              {/* Before image */}
              <Image
                src="/service/after.jpg"
                alt="Before treatment"
                width={800}
                height={400}
                className="absolute inset-0 w-full h-full object-cover"
              />

              {/* After image with clip */}
              <div
                className="absolute inset-0 overflow-hidden"
                style={{ clipPath: `inset(0 ${100 - sliderPosition}% 0 0)` }}
              >
                <Image
                  src="/service/before.jpg"
                  alt="After treatment"
                  width={800}
                  height={400}
                  className="w-full h-full object-cover"
                />
              </div>

              <div
                className="absolute top-0 bottom-0 w-1 bg-white cursor-ew-resize flex items-center justify-center"
                style={{ left: `${sliderPosition}%` }}
                onMouseDown={(e) => {
                  const rect = e.currentTarget.parentElement?.getBoundingClientRect()
                  if (!rect) return

                  const handleMouseMove = (e: MouseEvent) => {
                    const x = e.clientX - rect.left
                    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100))
                    setSliderPosition(percentage)
                  }

                  const handleMouseUp = () => {
                    document.removeEventListener("mousemove", handleMouseMove)
                    document.removeEventListener("mouseup", handleMouseUp)
                  }

                  document.addEventListener("mousemove", handleMouseMove)
                  document.addEventListener("mouseup", handleMouseUp)
                }}
              >
                <div className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center gap-2">
                  <div className="w-4 h-4 bg-gray-400 rounded"></div>
                  <div className="w-4 h-4 bg-gray-400 rounded"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
