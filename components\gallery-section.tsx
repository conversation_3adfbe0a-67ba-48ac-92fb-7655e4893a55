"use client"

import { useState } from "react"
import Image from "next/image"
import { motion, AnimatePresence } from "framer-motion"
import { useTranslations, useLocale } from "next-intl"

export default function GallerySection() {
  const t = useTranslations("gallery")
  const locale = useLocale()
  const [activeFilter, setActiveFilter] = useState("all")

  const filters = [
    { value: "all", label: t("all") },
    { value: "examination", label: t("examination") },
    { value: "reception", label: t("reception") },
    { value: "equipment", label: t("equipment") },
    { value: "facility", label: t("facility") },
    { value: "customer", label: t("customer") },
  ]

  const galleryImages = [
    { id: 1, category: "facility", src: "/gallery/1.jpg" },
    { id: 2, category: "examination", src: "/gallery/2.jpg" },
    { id: 4, category: "equipment", src: "/gallery/4.jpg" },
    { id: 6, category: "reception", src: "/gallery/6.jpg" },
    { id: 5, category: "facility", src: "/gallery/5.jpg" },
    { id: 8, category: "equipment", src: "/gallery/8.jpg" },
    { id: 10, category: "customer", src: "/gallery/10.jpg" },
    { id: 11, category: "customer", src: "/gallery/11.jpg" },
    { id: 9, category: "examination", src: "/gallery/9.jpg" },
  ]

  const filteredImages =
    activeFilter === "all"
      ? galleryImages
      : galleryImages.filter((image) => image.category === activeFilter)

  return (
    <section className="py-20 bg-gray-50" id="gallery">
      <div className="container mx-auto px-4 max-w-[1124px]">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-800 mb-4">{t("title")}</h2>
          <div className="flex items-center justify-center mb-6">
            <div className="h-px bg-teal-500 w-16"></div>
            <div className="w-3 h-3 bg-teal-500 rounded-full mx-3 flex items-center justify-center">
              <div className="w-1 h-1 bg-white rounded-full"></div>
            </div>
            <div className="h-px bg-teal-500 w-16"></div>
          </div>
          <div className="max-w-2xl mx-auto mb-8">
            <p className="text-gray-600 mb-2">{t("description")}</p>
          </div>
        </div>

        {/* Filter Tabs */}
        <div className="flex items-center justify-center flex-wrap gap-4 mb-12">
          {filters.map((filter) => (
            <button
              key={filter.value}
              onClick={() => setActiveFilter(filter.value)}
              className={`px-6 py-2 rounded-full transition-colors text-sm font-medium
                ${activeFilter === filter.value
                  ? "bg-teal-500 text-white"
                  : "text-gray-600 hover:text-teal-500 border border-gray-300"}
              `}
            >
              {filter.label}
            </button>
          ))}
        </div>

        {/* Masonry Gallery */}
        <div className="columns-1 sm:columns-2 md:columns-3 gap-4 space-y-4">
          <AnimatePresence>
            {filteredImages.map((image) => (
              <motion.div
                key={image.id}
                layout
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{ duration: 0.3 }}
                className="w-full break-inside-avoid overflow-hidden rounded-lg shadow-sm hover:shadow-md transition-shadow"
              >
                <Image
                  src={image.src || "/placeholder.svg"}
                  alt={`Gallery ${image.id}`}
                  width={600}
                  height={400}
                  className="w-full h-auto object-cover transition-transform duration-300 hover:scale-105"
                />
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      </div>
    </section>
  )
}
