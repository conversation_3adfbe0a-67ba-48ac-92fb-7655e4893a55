"use client"

import { useTranslations } from 'next-intl';
import { useRouter, usePathname } from 'next/navigation';
import { Button } from './ui/button';
import { Globe } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from './ui/dropdown-menu';

export default function LanguageSwitcher() {
  const t = useTranslations('common');
  const router = useRouter();
  const pathname = usePathname();

  // Get current locale from pathname instead of useLocale()
  const getCurrentLocale = () => {
    const segments = pathname.split('/').filter(Boolean);
    if (segments.length > 0 && ['en', 'vi'].includes(segments[0])) {
      return segments[0];
    }
    return 'vi'; // default
  };

  const currentLocale = getCurrentLocale();

  const switchLanguage = (newLocale: string) => {
    // Get the path without any locale prefix
    const segments = pathname.split('/').filter(Boolean);

    // Remove the first segment if it's a locale
    if (segments.length > 0 && ['en', 'vi'].includes(segments[0])) {
      segments.shift();
    }

    // Construct the new path with the new locale
    const pathWithoutLocale = segments.length > 0 ? `/${segments.join('/')}` : '';
    const newPath = `/${newLocale}${pathWithoutLocale}`;

    router.push(newPath);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          {currentLocale === 'vi' ? '🇻🇳 ' + t('vietnamese') : '🇺🇸 ' + t('english')}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem
          onClick={() => switchLanguage('en')}
          className={currentLocale === 'en' ? 'bg-accent' : ''}
        >
          🇺🇸 {t('english')}
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => switchLanguage('vi')}
          className={currentLocale === 'vi' ? 'bg-accent' : ''}
        >
          🇻🇳 {t('vietnamese')}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
