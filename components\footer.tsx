"use client"

import Link from "next/link"
import { Facebook, Linkedin, Instagram, Twitter, ChevronUp } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useTranslations } from 'next-intl'
import Image from "next/image"

export default function Footer() {
  const t = useTranslations('footer')
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" })
  }

  return (
    <footer className="bg-[url(/footer/footer.png)] pt-16 relative">
      <div className="container mx-auto px-4 max-w-[1124px]">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-8 mb-12">
          <div className="col-span-1 md:col-span-2">
            <Image
              src="/logo/logo.png"
              alt="SGI Dental"
              width={120}
              height={120}
            />
            <p className="text-gray-600 mb-2 leading-relaxed">
              {t('subtitle')}
            </p>
            <p className="text-gray-600 mb-6 leading-relaxed">
              {t('description')}
            </p>
            <div className="flex space-x-3 items-center">
              <a
                href="https://www.facebook.com/sgidental"
                target="_blank"
                rel="noopener noreferrer"
                aria-label="Facebook"
              >
                <Facebook className="w-5 h-5 text-teal-500 cursor-pointer hover:text-teal-600" />
              </a>
              <a
                href="https://www.instagram.com/sgidental/"
                target="_blank"
                rel="noopener noreferrer"
                aria-label="Instagram"
              >
                <Instagram className="w-5 h-5 text-teal-500 cursor-pointer hover:text-teal-600" />
              </a>
            </div>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-teal-500 mb-4">{t('quickLinks')}</h3>
            <ul className="space-y-3">
              <li>
                <Link href="#" className="text-gray-600 hover:text-teal-500 flex items-center">
                  <span className="mr-2">›</span> {t('faq')}
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-600 hover:text-teal-500 flex items-center">
                  <span className="mr-2">›</span> {t('highlightTeam')}
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-600 hover:text-teal-500 flex items-center">
                  <span className="mr-2">›</span> {t('highlightSchedule')}
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-600 hover:text-teal-500 flex items-center">
                  <span className="mr-2">›</span> {t('highlightPolicy')}
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-teal-500 mb-4">{t("departments")}</h3>
            <ul className="space-y-3">
              <li>
                <Link href="#" className="text-gray-600 hover:text-teal-500 flex items-center">
                  <span className="mr-2">›</span> {t('service1')}
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-600 hover:text-teal-500 flex items-center">
                  <span className="mr-2">›</span> {t('service2')}
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-600 hover:text-teal-500 flex items-center">
                  <span className="mr-2">›</span> {t('service3')}
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-600 hover:text-teal-500 flex items-center">
                  <span className="mr-2">›</span> {t('service4')}
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-teal-500 mb-4">{t('contact')}</h3>
            <div className="space-y-4">
              <div>
                <p className="font-medium text-gray-700">{t('address')}</p>
                <p className="text-gray-600">{t('addressText')}</p>
              </div>
              <div>
                <p className="font-medium text-gray-700">{t('email')}</p>
                <p className="text-gray-600"><EMAIL></p>
              </div>
              <div>
                <p className="font-medium text-gray-700">{t('hotline')}</p>
                <p className="text-gray-600">079 883 6779</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="bg-gray-200 border-t border-gray-200 py-8">
        <div className="container mx-auto px-4 max-w-[1124px] flex flex-col md:flex-row items-center justify-between gap-6">
          <div className="flex items-center justify-center">
            <p className="text-gray-600 text-lg text-center">
              {t('copyright')}</p>
          </div>
          <div>
            <p className="text-gray-600 text-lg">
              {t('developedBy')} <a
                href="https://neyul.com"
                target="_blank"
                rel="noopener noreferrer"
                className="hover:underline hover:underline-offset-2 transition-all duration-300"
              >
                NEYUL MARKETING
              </a></p>
          </div>
        </div>
      </div>

      <Button
        onClick={scrollToTop}
        className="fixed bottom-6 right-6 w-12 h-12 rounded-full bg-teal-500 hover:bg-teal-600 shadow-lg"
        size="icon"
      >
        <ChevronUp className="w-5 h-5" />
      </Button>
    </footer>
  )
}
