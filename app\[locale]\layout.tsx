import type { Metadata } from "next";
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { notFound } from 'next/navigation';
import { Toaster } from '@/components/ui/toaster';
import GoogleAnalytics from '@/components/googleanalytics';
import { Inter } from "next/font/google";
const inter = Inter({ subsets: ["latin"] });

const locales = ['vi', 'en'];

export const metadata: Metadata = {
  metadataBase: new URL("https://nhakhoasgi.com"),
  title: "SGI Dental Nụ cười khỏe đẹp - Dịch vụ tận tâm",
  description:
    "SGI Dental là phòng khám nha khoa thẩm mỹ và điều trị tổng quát theo tiêu chuẩn quốc tế, với đội ngũ bác sĩ chuyên môn cao, công nghệ hiện đại và không gian điều trị cao cấp tại Estella Place.",
  openGraph: {
    title: "SGI Dental Nụ cười khỏe đẹp - Dịch vụ tận tâm",
    description:
      "SGI Dental là phòng khám nha khoa thẩm mỹ và điều trị tổng quát theo tiêu chuẩn quốc tế, với đội ngũ bác sĩ chuyên môn cao, công nghệ hiện đại và không gian điều trị cao cấp tại Estella Place.",
    url: "https://medical-two-tau.vercel.app",
    siteName: "SGI Dental",
    images: [
      {
        url: "https://medical-two-tau.vercel.app/logo/logo.png",
        width: 1200,
        height: 630,
        alt: "SGI Dental Nụ cười khỏe đẹp - Dịch vụ tận tâm",
      },
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    site: "@nhakhoasgi",
    title: "SGI Dental Nụ cười khỏe đẹp - Dịch vụ tận tâm",
    description: "SGI Dental là phòng khám nha khoa thẩm mỹ và điều trị tổng quát theo tiêu chuẩn quốc tế, với đội ngũ bác sĩ chuyên môn cao, công nghệ hiện đại và không gian điều trị cao cấp tại Estella Place.",
    images: ["https://medical-two-tau.vercel.app/logo/logo.png"],
  },
  alternates: {
    canonical: "https://nhakhoasgi.com",
  },
  robots: {
    index: true,
    follow: true,
    nocache: false,
    googleBot: {
      index: true,
      follow: true,
      noimageindex: false,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}


export default async function LocaleLayout({
  children,
  params
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;

  if (!locales.includes(locale as any)) {
    notFound();
  }

  const messages = await getMessages({ locale });

  return (
    <html lang={locale}>
      <head>
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <meta name="theme-color" content="#ffffff" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="preconnect" href="https://www.googletagmanager.com" />
        <link rel="dns-prefetch" href="https://www.googletagmanager.com" />
      </head>
      <body className={inter.className}>
        <NextIntlClientProvider messages={messages}>
          {children}
          <Toaster />
          <GoogleAnalytics /> 
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
