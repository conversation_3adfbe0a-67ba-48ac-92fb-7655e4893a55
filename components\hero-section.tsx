"use client"
import { useState } from "react"
import { useTranslations } from 'next-intl'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"

export default function HeroSection() {
  const t = useTranslations('hero')
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    date: "",
    time: "",
    service: "",
    message: ""
  })

  const generateTimeSlots = () => {
    const slots = []
    for (let hour = 8; hour <= 18; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        if (hour === 18 && minute > 0) break 
        const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
        const displayTime = new Date(`1970-01-01T${timeString}`).toLocaleTimeString('vi-VN', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        })
        slots.push({ value: timeString, display: displayTime })
      }
    }
    return slots
  }

  const timeSlots = generateTimeSlots()

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: string) => (value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.name || !formData.email || !formData.phone) {
      toast({
        title: t('form.missingInfo'),
        description: t('form.missingInfoDesc'),
        variant: "destructive"
      })
      return
    }

    setIsSubmitting(true)

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        toast({
          title: t('form.successTitle'),
          description: t('form.successDesc'),
        })

        // Reset form
        setFormData({
          name: "",
          email: "",
          phone: "",
          date: "",
          time: "",
          service: "",
          message: ""
        })
      } else {
        throw new Error('Failed to submit form')
      }
    } catch (error) {
      toast({
        title: t('form.errorTitle'),
        description: t('form.errorDesc'),
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }
  
  const serviceKeys = ["examination", "scaling", "whitening", "braces", "crown", "implant"]
  
  return (
    <section className="relative min-h-screen overflow-hidden">
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url('/banner/banner.jpg')`
        }}
      ></div>

      <div className="absolute inset-0 opacity-10">
        <div className="absolute right-0 top-0 w-1/2 h-full">
          <div className="w-full h-full relative">
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_2px_2px,rgba(255,255,255,0.3)_1px,transparent_0)] bg-[length:20px_20px]"></div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-20 relative z-10 max-w-[1124px]">
        <div className="grid lg:grid-cols-2 gap-12 items-center min-h-[80vh]">
          <div className="text-white">
            <h1 className="text-3xl lg:text-5xl font-bold md:leading-loose mb-6">
              {t('title')}
              <br />
              {t('subtitle')}
            </h1>
            <p className="text-xl mb-8 opacity-90 drop-shadow-md">
              {t('description')}
            </p>
          </div>

          <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-8 shadow-2xl">
            <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">{t('bookingTitle')}</h2>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <Input
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder={t('form.namePlaceholder')}
                  className="py-3"
                />
                <Input
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  placeholder={t('form.emailPlaceholder')}
                  type="email"
                  className="py-3"
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <Input
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  placeholder={t('form.phonePlaceholder')}
                  type="tel"
                  className="py-3"
                />
                <Input
                  name="date"
                  value={formData.date}
                  onChange={handleChange}
                  placeholder={t('form.datePlaceholder')}
                  type="date"
                  className="py-3"
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <Select onValueChange={handleSelectChange('time')} value={formData.time}>
                  <SelectTrigger className="py-3">
                    <SelectValue placeholder={t('form.timePlaceholder') || 'Chọn giờ'} />
                  </SelectTrigger>
                  <SelectContent>
                    {timeSlots.map((slot) => (
                      <SelectItem key={slot.value} value={slot.value}>
                        {slot.display}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select onValueChange={handleSelectChange('service')} value={formData.service}>
                  <SelectTrigger className="py-3">
                    <SelectValue placeholder={t('form.servicePlaceholder')} />
                  </SelectTrigger>
                  <SelectContent>
                    {serviceKeys.map((key) => (
                      <SelectItem key={key} value={key}>
                        {t(`form.services.${key}`)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <Textarea
                name="message"
                value={formData.message}
                onChange={handleChange}
                placeholder={t('form.messagePlaceholder')}
                className="min-h-[100px]"
              />
              <Button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-teal-500 hover:bg-teal-600 py-3 text-lg rounded-full"
              >
                {isSubmitting ? t('form.submitting') : t('form.submitButton')}
              </Button>
            </form>
          </div>
        </div>
      </div>
    </section>
  )
}