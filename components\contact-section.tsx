"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { useTranslations } from 'next-intl'
import { useToast } from "@/hooks/use-toast"
import Image from "next/image"

export default function ContactSection() {
  const t = useTranslations('contact')
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    subject: "",
    phone: "",
    message: ""
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Basic validation
    if (!formData.fullName || !formData.email || !formData.message) {
      toast({
        title: "Thông tin thiếu",
        description: "Vui lòng điền đầy đủ họ tên, email và tin nhắn.",
        variant: "destructive"
      })
      return
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(formData.email)) {
      toast({
        title: "Email không hợp lệ",
        description: "Vui lòng nhập địa chỉ email đúng định dạng.",
        variant: "destructive"
      })
      return
    }

    setIsSubmitting(true)

    try {
      // Prepare data for contact API (reuse existing API structure)
      const contactData = {
        name: formData.fullName,
        email: formData.email,
        phone: formData.phone,
        subject: formData.subject,
        message: formData.message,
        date: "",
        time: "",
        service: "contact_inquiry"
      }

      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(contactData),
      })

      if (response.ok) {
        toast({
          title: "Gửi thành công!",
          description: "Cảm ơn bạn đã liên hệ. Chúng tôi sẽ phản hồi sớm nhất có thể.",
        })

        // Reset form
        setFormData({
          fullName: "",
          email: "",
          subject: "",
          phone: "",
          message: ""
        })
      } else {
        throw new Error('Failed to submit form')
      }
    } catch (error) {
      toast({
        title: "Có lỗi xảy ra",
        description: "Không thể gửi tin nhắn. Vui lòng thử lại sau.",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }
  return (
    <section className="relative py-20 bg-white overflow-hidden" id="contact">
      <Image
        src="https://html.laralink.com/nischinto/nischinto/assets/img/shape/contact-shape1.svg"
        alt="shape top left"
        width={1000}
        height={1000}
        className="absolute top-0 left-0 -translate-x-1/3 -translate-y-1/3 pointer-events-none z-0"
      />

      <Image
        src="https://html.laralink.com/nischinto/nischinto/assets/img/shape/contact-shape2.svg"
        alt="shape bottom right"
        width={1000}
        height={1000}
        className="absolute bottom-0 right-0 translate-x-1/3 translate-y-1/3 pointer-events-none z-0"
      />

      <div className="container mx-auto px-4 max-w-[1124px] relative z-10">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">{t('title')}</h2>
          <div className="flex items-center justify-center mb-6">
            <div className="h-px bg-teal-500 w-16"></div>
            <div className="w-3 h-3 bg-teal-500 rounded-full mx-3 flex items-center justify-center">
              <div className="w-1 h-1 bg-white rounded-full"></div>
            </div>
            <div className="h-px bg-teal-500 w-16"></div>
          </div>
          <div className="max-w-2xl mx-auto">
            <p className="text-gray-600 mb-2">
              {t('description1')}
            </p>
            <p className="text-gray-600">{t('description2')}</p>
          </div>
        </div>

        <div className="max-w-4xl mx-auto">
          <form onSubmit={handleSubmit}>
            <div className="grid md:grid-cols-2 gap-6 mb-6">
              <div>
                <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('form.fullName')}
                </label>
                <Input
                  id="fullName"
                  name="fullName"
                  value={formData.fullName}
                  onChange={handleChange}
                  placeholder={t('form.fullNamePlaceholder')}
                  className="w-full py-3"
                  required
                />
              </div>
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('form.email')}
                </label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  placeholder={t('form.emailPlaceholder')}
                  className="w-full py-3"
                  required
                />
              </div>
            </div>

            <div className="grid md:grid-cols-2 gap-6 mb-6">
              <div>
                <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('form.subject')}
                </label>
                <Input
                  id="subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleChange}
                  placeholder={t('form.subjectPlaceholder')}
                  className="w-full py-3"
                />
              </div>
              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('form.phone')}
                </label>
                <Input
                  id="phone"
                  name="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={handleChange}
                  placeholder={t('form.phonePlaceholder')}
                  className="w-full py-3"
                />
              </div>
            </div>

            <div className="mb-6">
              <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                {t('form.message')}
              </label>
              <Textarea
                id="message"
                name="message"
                value={formData.message}
                onChange={handleChange}
                placeholder={t('form.messagePlaceholder')}
                className="w-full min-h-[120px]"
                required
              />
            </div>

            <div className="text-center">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-teal-500 hover:bg-teal-600 px-8 py-3 rounded-full disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? "Đang gửi..." : t('form.sendButton')}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </section>
  )
}
