"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { useTranslations } from 'next-intl'
import Image from "next/image"

export default function ContactSection() {
  const t = useTranslations('contact')
  return (
    <section className="relative py-20 bg-white overflow-hidden" id="contact">
      {/* Top Left SVG */}
      <Image
        src="https://html.laralink.com/nischinto/nischinto/assets/img/shape/contact-shape1.svg"
        alt="shape top left"
        width={1000}
        height={1000}
        className="absolute top-0 left-0 -translate-x-1/3 -translate-y-1/3 pointer-events-none z-0"
      />

      {/* Bottom Right SVG */}
      <Image
        src="https://html.laralink.com/nischinto/nischinto/assets/img/shape/contact-shape2.svg"
        alt="shape bottom right"
        width={1000}
        height={1000}
        className="absolute bottom-0 right-0 translate-x-1/3 translate-y-1/3 pointer-events-none z-0"
      />

      {/* Content */}
      <div className="container mx-auto px-4 max-w-[1124px] relative z-10">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-800 mb-4">{t('title')}</h2>
          <div className="flex items-center justify-center mb-6">
            <div className="h-px bg-teal-500 w-16"></div>
            <div className="w-3 h-3 bg-teal-500 rounded-full mx-3 flex items-center justify-center">
              <div className="w-1 h-1 bg-white rounded-full"></div>
            </div>
            <div className="h-px bg-teal-500 w-16"></div>
          </div>
          <div className="max-w-2xl mx-auto">
            <p className="text-gray-600 mb-2">
              {t('description1')}
            </p>
            <p className="text-gray-600">{t('description2')}</p>
          </div>
        </div>

        <div className="max-w-4xl mx-auto">
          <form>
            <div className="grid md:grid-cols-2 gap-6 mb-6">
              <div>
                <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('form.fullName')}
                </label>
                <Input id="fullName" placeholder={t('form.fullNamePlaceholder')} className="w-full py-3" />
              </div>
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('form.email')}
                </label>
                <Input id="email" type="email" placeholder={t('form.emailPlaceholder')} className="w-full py-3" />
              </div>
            </div>

            <div className="grid md:grid-cols-2 gap-6 mb-6">
              <div>
                <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('form.subject')}
                </label>
                <Input id="subject" placeholder={t('form.subjectPlaceholder')} className="w-full py-3" />
              </div>
              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('form.phone')}
                </label>
                <Input id="phone" type="tel" placeholder={t('form.phonePlaceholder')} className="w-full py-3" />
              </div>
            </div>

            <div className="mb-6">
              <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                {t('form.message')}
              </label>
              <Textarea id="message" placeholder={t('form.messagePlaceholder')} className="w-full min-h-[120px]" />
            </div>

            <div className="text-center">
              <Button className="bg-teal-500 hover:bg-teal-600 px-8 py-3 rounded-full">{t('form.sendButton')}</Button>
            </div>
          </form>
        </div>
      </div>
    </section>
  )
}
