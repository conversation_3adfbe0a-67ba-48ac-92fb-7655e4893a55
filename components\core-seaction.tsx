"use client"
import Image from "next/image"
import { useTranslations } from 'next-intl'
import Link from "next/link"

export default function CoreSection() {
  const t = useTranslations('about')
  const tDept = useTranslations('departments')

  const departments = [
    {
      key: "examination",
      name: tDept('examination.name'),
      img: "/service/kham-rang.jpg",
      bgColor: "bg-teal-500",
      imageSeaction: "/home/<USER>",
      desc1: tDept('examination.desc1'),
      desc2: tDept('examination.desc2'),
    },
    {
      key: "scaling",
      name: tDept('scaling.name'),
      img: "/service/lay-voi.png",
      bgColor: "bg-red-100",
      imageSeaction: "/home/<USER>",
      desc1: tDept('scaling.desc1'),
      desc2: tDept('scaling.desc2'),
    },
    {
      key: "whitening",
      name: tDept('whitening.name'),
      img: "/service/kham-rang.jpg",
      bgColor: "bg-green-100",
      imageSeaction: "/home/<USER>",
      desc1: tDept('whitening.desc1'),
      desc2: tDept('whitening.desc2'),
    },
    {
      key: "braces",
      name: tDept('braces.name'),
      img: "/service/kham-rang.jpg",
      bgColor: "bg-blue-100",
      imageSeaction: "/home/<USER>",
      desc1: tDept('braces.desc1'),
      desc2: tDept('braces.desc2'),
    },
    {
      key: "crown",
      name: tDept('crown.name'),
      img: "/service/kham-rang.jpg",
      bgColor: "bg-yellow-100",
      imageSeaction: "/home/<USER>",
      desc1: tDept('crown.desc1'),
      desc2: tDept('crown.desc2'),
    },
    {
      key: "implant",
      name: tDept('implant.name'),
      img: "/service/kham-rang.jpg",
      bgColor: "bg-purple-100",
      imageSeaction: "/home/<USER>",
      desc1: tDept('implant.desc1'),
      desc2: tDept('implant.desc2'),
    },
    {
      key: "pediatric",
      name: tDept('pediatric.name'),
      img: "/service/kham-rang.jpg",
      bgColor: "bg-purple-100",
      imageSeaction: "/home/<USER>",
      desc1: tDept('pediatric.desc1'),
      desc2: tDept('pediatric.desc2'),
    },
  ]

  return (
    <section id="service">
      <div className="container mx-auto px-4 max-w-[1124px]">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">{t('departmentTitle')}</h2>
          <div className="flex items-center justify-center mb-6">
            <div className="h-px bg-teal-500 w-16"></div>
            <div className="w-3 h-3 bg-teal-500 rounded-full mx-3 flex items-center justify-center">
              <div className="w-1 h-1 bg-white rounded-full"></div>
            </div>
            <div className="h-px bg-teal-500 w-16"></div>
          </div>
          <div className="max-w-4xl mx-auto">
            <p className="text-gray-600 mb-2">
              {t('departmentDescription1')}
            </p>
            <p className="text-gray-600">{t('departmentDescription2')}</p>
          </div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-7 gap-4 mb-20">
          {departments.map((dept, index) => {
            return (
              <div
                key={index}
                className="text-center group cursor-pointer"
              >
                <div
                  className={`p-6 rounded-t-lg flex items-center justify-center mx-auto text-gray-600`
                  }
                >
                  <Image
                    src={dept.img}
                    alt="Medical professionals"
                    width={120}
                    height={120}
                    className="rounded-lg"
                  />
                </div>
                <h3
                  className=
                  {`font-medium pb-4 rounded-b-lg text-gray-600`
                  }
                >
                  {dept.name}
                </h3>
              </div>
            )
          })}
        </div>
      </div>
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 max-w-[1124px]">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="grid grid-cols-2 gap-4">
              <div className="relative aspect-square rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
                <Image
                  src="/gallery/9.jpg"
                  alt="Bác sĩ tư vấn khách hàng"
                  width={250}
                  height={250}
                  className="object-cover w-full h-full hover:scale-105 transition-transform duration-300"
                />
              </div>

              <div className="relative aspect-square rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
                <Image
                  src="/gallery/2.jpg"
                  alt="Đội ngũ bác sĩ SGI Dental"
                  width={250}
                  height={250}
                  className="object-cover w-full h-full hover:scale-105 transition-transform duration-300"
                />
              </div>

              <div className="relative aspect-square rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
                <Image
                  src="/gallery/1.jpg"
                  alt="Khám và điều trị nha khoa"
                  width={250}
                  height={250}
                  className="object-cover w-full h-full hover:scale-105 transition-transform duration-300"
                />
              </div>

              <div className="relative aspect-square rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
                <Image
                  src="/gallery/4.jpg"
                  alt="Tư vấn chuyên sâu tại ECHO MEDI"
                  width={250}
                  height={250}
                  className="object-cover w-full h-full hover:scale-105 transition-transform duration-300"
                />
              </div>
            </div>
            <div>
              <h2 className="text-3xl font-bold mb-6">
                {t('welcomeHeading')} <span className="text-teal-500">SGI Dental</span>
              </h2>
              <p className="text-gray-600 mb-6 leading-relaxed">
                {t('welcomeDescription')}
              </p>

              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-teal-500 rounded-full mt-2 flex-shrink-0" />
                  <p className="text-gray-600">{t('point1')}</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-teal-500 rounded-full mt-2 flex-shrink-0" />
                  <p className="text-gray-600">{t('point2')}</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-teal-500 rounded-full mt-2 flex-shrink-0" />
                  <p className="text-gray-600">{t('point3')}</p>
                </div>
              </div>

              <div className="mt-8">
                <Link href="#">
                  <button className="bg-teal-500 text-white px-6 py-3 rounded-full hover:bg-teal-600 transition-colors duration-300 font-medium">
                    {t('ctaButton')}
                  </button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </section>
  )
}
