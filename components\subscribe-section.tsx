"use client"

import { Input } from "@/components/ui/input"
import { Send } from "lucide-react"

export default function SubscribeSection() {
  return (
    <section className="bg-[url(https://html.laralink.com/nischinto/nischinto/assets/img/news-letter-bg.png)] bg-cover bg-center bg-no-repeat">
      <div className="container mx-auto px-4 max-w-[1124px] py-20">
        <div className="text-center">
          <h2 className="text-4xl font-bold text-gray-800 mb-4">Subscribe & stay updated</h2>
          <div className="flex items-center justify-center mb-6">
            <div className="h-px bg-teal-500 w-16"></div>
            <div className="w-3 h-3 bg-teal-500 rounded-full mx-3 flex items-center justify-center">
              <div className="w-1 h-1 bg-white rounded-full"></div>
            </div>
            <div className="h-px bg-teal-500 w-16"></div>
          </div>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Sign up to our newsletter and be the first to know about latest news,<br />
            special offers, events, and discounts.
          </p>
        </div>

        {/* Input with lucide send icon and border-left */}
        <div className="max-w-md mx-auto my-16 relative">
          <div className="relative">
            <Input
              type="email"
              placeholder="Enter Your Email Address"
              className="py-4 pr-14 rounded-md shadow-md"
            />
            <div className="absolute top-1/2 right-3 -translate-y-1/2 h-6 border-l pl-3 border-gray-300 flex items-center">
              <button className="text-teal-500 hover:text-teal-600" aria-label="Send">
                <Send size={18} strokeWidth={2} />
              </button>
            </div>
          </div>
        </div>

        {/* Phone number */}
        <div className="text-center text-teal-500 font-semibold text-xl">
          (+01) - <span className="font-bold">234 567 890</span>
        </div>
      </div>
    </section>
  )
}
