"use client"
import { useState, useEffect } from "react"
import Link from "next/link"
import { useTranslations } from "next-intl"
import LanguageSwitcher from "./language-switcher"
import { Mail, Phone, Facebook, Linkedin, Youtube, Twitter, Menu, Instagram } from "lucide-react"
import Image from "next/image"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"

export default function Nav() {
  const t = useTranslations("navigation")
  const [isScrolled, setIsScrolled] = useState(false)
  const [activeLink, setActiveLink] = useState("#")
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const navLinks = [
    { href: "#", label: t("home") },
    { href: "#about", label: t("about") },
    { href: "#service", label: t("services") },
    { href: "#doctor", label: t("doctors") },
    { href: "#gallery", label: t("gallery") },
    { href: "#pricing", label: t("pricing") },
    { href: "#contact", label: t("contact") },
  ]

  const handleMobileLinkClick = (href: string) => {
    setActiveLink(href)
    setIsMobileMenuOpen(false)
  }

  return (
    <>
      <div
        className={`bg-white text-black transition-all duration-300 hidden md:block ${isScrolled ? "h-0 overflow-hidden opacity-0" : ""
          }`}
      >
        <div className="bg-gray-50 py-2">
          <div className="container mx-auto px-4 flex justify-between items-center text-sm text-gray-600 max-w-[1124px]">
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <Mail className="w-4 h-4" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center space-x-2">
                <Phone className="w-4 h-4" />
                <span>************</span>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <a
                href="https://www.facebook.com/sgidental"
                target="_blank"
                rel="noopener noreferrer"
                aria-label="Facebook"
              >
                <Facebook className="w-5 h-5 cursor-pointer " />
              </a>
              <a
                href="https://www.instagram.com/sgidental/"
                target="_blank"
                rel="noopener noreferrer"
                aria-label="Instagram"
              >
                <Instagram className="w-5 h-5 cursor-pointer" />
              </a>
            </div>
          </div>
        </div>
      </div>

      <header className={`sticky top-0 z-50 w-full ${isScrolled ? "shadow-md" : ""} bg-white`}>
        <nav className="bg-white py-1">
          <div className="container mx-auto px-4 flex justify-between items-center max-w-[1124px]">
            {/* Logo */}
            <Image src="/logo/logo.png" alt="SGI Dental" width={120} height={120} className="w-auto h-12 md:h-16" />

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              {navLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  onClick={() => setActiveLink(link.href)}
                  className={`transition-colors ${activeLink === link.href
                    ? "text-teal-500 font-medium border-b-2 border-teal-500 pb-1"
                    : "text-gray-600 hover:text-teal-500"
                    }`}
                >
                  {link.label}
                </Link>
              ))}
              <LanguageSwitcher />
            </div>

            <div className="flex items-center space-x-3 md:hidden">
              <a
                href="tel:0798836779"
                className="flex items-center justify-center w-10 h-10 bg-teal-500 text-white rounded-full hover:bg-teal-600 transition-colors"
                aria-label="Call ************"
              >
                <Phone className="w-5 h-5" />
              </a>

              <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
                <SheetTrigger asChild>
                  <button
                    className="p-2 text-gray-600 hover:text-teal-500 transition-colors"
                    aria-label="Toggle mobile menu"
                  >
                    <Menu className="w-6 h-6" />
                  </button>
                </SheetTrigger>
                <SheetContent side="right" className="w-full">
                  <div className="flex flex-col h-full">
                    <div className="flex items-center justify-center py-6 border-b border-gray-100">
                      <Image src="/logo/logo.png" alt="SGI Dental" width={120} height={120} className="w-auto h-12" />
                    </div>
                    <div className="flex items-end justify-end mt-8">
                      <LanguageSwitcher />
                    </div>
                    <div className="flex flex-col space-y-2 py-6 flex-1">
                      {navLinks.map((link) => (
                        <Link
                          key={link.href}
                          href={link.href}
                          onClick={() => handleMobileLinkClick(link.href)}
                          className={`py-3 px-4 rounded-lg transition-colors text-lg ${activeLink === link.href
                            ? "text-teal-500 font-medium bg-teal-50"
                            : "text-gray-600 hover:text-teal-500 hover:bg-gray-50"
                            }`}
                        >
                          {link.label}
                        </Link>
                      ))}
                    </div>
                  </div>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </nav>
      </header>
    </>
  )
}
