"use client"

import { useState } from "react"
import Image from "next/image"
import { useTranslations } from 'next-intl'
import { Button } from "./ui/button"
import { Cardiology, XRay, Dentalcare, Neurology, Pulmonary, Crutches } from "./svg"

export default function AboutSection() {
  const t = useTranslations('about')
  const tDept = useTranslations('departments')

  const departments = [
    {
      key: "examination",
      name: tDept('examination.name'),
      img: "/service/kham-rang.jpg",
      bgColor: "bg-teal-500",
      imageSeaction: "/home/<USER>",
      desc1: tDept('examination.desc1'),
      desc2: tDept('examination.desc2'),
    },
    {
      key: "scaling",
      name: tDept('scaling.name'),
      img: "/service/lay-voi.png",
      bgColor: "bg-red-100",
      imageSeaction: "/home/<USER>",
      desc1: tDept('scaling.desc1'),
      desc2: tDept('scaling.desc2'),
    },
    {
      key: "whitening",
      name: tDept('whitening.name'),
      img: "/service/kham-rang.jpg",
      bgColor: "bg-green-100",
      imageSeaction: "/home/<USER>",
      desc1: tDept('whitening.desc1'),
      desc2: tDept('whitening.desc2'),
    },
    {
      key: "braces",
      name: tDept('braces.name'),
      img: "/service/kham-rang.jpg",
      bgColor: "bg-blue-100",
      imageSeaction: "/home/<USER>",
      desc1: tDept('braces.desc1'),
      desc2: tDept('braces.desc2'),
    },
    {
      key: "crown",
      name: tDept('crown.name'),
      img: "/service/kham-rang.jpg",
      bgColor: "bg-yellow-100",
      imageSeaction: "/home/<USER>",
      desc1: tDept('crown.desc1'),
      desc2: tDept('crown.desc2'),
    },
    {
      key: "implant",
      name: tDept('implant.name'),
      img: "/service/kham-rang.jpg",
      bgColor: "bg-purple-100",
      imageSeaction: "/home/<USER>",
      desc1: tDept('implant.desc1'),
      desc2: tDept('implant.desc2'),
    },
    {
      key: "pediatric",
      name: tDept('pediatric.name'),
      img: "/service/kham-rang.jpg",
      bgColor: "bg-purple-100",
      imageSeaction: "/home/<USER>",
      desc1: tDept('pediatric.desc1'),
      desc2: tDept('pediatric.desc2'),
    },
  ]

  const [selectedDept, setSelectedDept] = useState(departments[0])

  return (
    <section className="py-20" id="about">
      <div className="container mx-auto px-4 max-w-[1124px]">
        <div className="text-center">
          <h2 className="text-4xl font-bold text-gray-800 mb-4">{t('whoAreWe')}</h2>
          <div className="flex items-center justify-center mb-6">
            <div className="h-px bg-teal-500 w-16"></div>
            <div className="w-3 h-3 bg-teal-500 rounded-full mx-3 flex items-center justify-center">
              <div className="w-1 h-1 bg-white rounded-full"></div>
            </div>
            <div className="h-px bg-teal-500 w-16"></div>
          </div>
          <div className="max-w-2xl mx-auto">
            <p className="text-gray-600 mb-12">
              {t('whoDescription')}
            </p>
          </div>
        </div>
        <div className="grid lg:grid-cols-2 gap-12 items-center mb-20">
          <div>
            <h2 className="text-4xl font-bold text-gray-800 mb-6">
              {t('title')}
            </h2>
            <p className="text-gray-600 mb-6 leading-relaxed">
              {t('description1')}
            </p>
          </div>

          <div className="relative w-fit">
            <div className="absolute -bottom-6 -left-6 w-full h-full bg-cyan-100 rounded-lg -z-10" />
            <Image
              src="/home/<USER>"
              alt="Medical professionals"
              width={600}
              height={400}
              className="rounded-lg shadow-lg"
            />
          </div>
        </div>

        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-800 mb-4">{t('departmentTitle')}</h2>
          <div className="flex items-center justify-center mb-6">
            <div className="h-px bg-teal-500 w-16"></div>
            <div className="w-3 h-3 bg-teal-500 rounded-full mx-3 flex items-center justify-center">
              <div className="w-1 h-1 bg-white rounded-full"></div>
            </div>
            <div className="h-px bg-teal-500 w-16"></div>
          </div>
          <div className="max-w-4xl mx-auto">
            <p className="text-gray-600 mb-2">
              {t('departmentDescription1')}
            </p>
            <p className="text-gray-600">{t('departmentDescription2')}</p>
          </div>
        </div>

        {/* Department cards */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-7 gap-6 mb-20">
          {departments.map((dept, index) => {
            return (
              <div
                key={index}
                className="text-center group cursor-pointer"
              >
                <div
                  className={`p-6 rounded-t-lg flex items-center justify-center mx-auto text-gray-600`
                  }
                >
                  <Image
                    src={dept.img}
                    alt="Medical professionals"
                    width={120}
                    height={120}
                    className="rounded-lg"
                  />
                </div>
                <h3
                  className=
                  {`font-medium pb-4 rounded-b-lg text-gray-600`
                  }
                >
                  {dept.name}
                </h3>
              </div>
            )
          })}
        </div>
      </div>
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 max-w-[1124px]">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="grid grid-cols-2 gap-4">
              <div className="relative aspect-square rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
                <Image
                  src="/gallery/9.jpg"
                  alt="Bác sĩ tư vấn khách hàng"
                  width={250}
                  height={250}
                  className="object-cover w-full h-full hover:scale-105 transition-transform duration-300"
                />
              </div>

              <div className="relative aspect-square rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
                <Image
                  src="/gallery/2.jpg"
                  alt="Đội ngũ bác sĩ SGI Dental"
                  width={250}
                  height={250}
                  className="object-cover w-full h-full hover:scale-105 transition-transform duration-300"
                />
              </div>

              <div className="relative aspect-square rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
                <Image
                  src="/gallery/1.jpg"
                  alt="Khám và điều trị nha khoa"
                  width={250}
                  height={250}
                  className="object-cover w-full h-full hover:scale-105 transition-transform duration-300"
                />
              </div>

              <div className="relative aspect-square rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
                <Image
                  src="/gallery/4.jpg"
                  alt="Tư vấn chuyên sâu tại ECHO MEDI"
                  width={250}
                  height={250}
                  className="object-cover w-full h-full hover:scale-105 transition-transform duration-300"
                />
              </div>
            </div>

            <div>
              <h2 className="text-3xl font-bold text-gray-800 mb-6">
                Chào mừng đến với SGI Dental
              </h2>
              <p className="text-gray-600 mb-6 leading-relaxed">
                Nơi mà khách hàng sẽ được trải nghiệm dịch vụ nha khoa chuẩn quốc tế,
                với trang thiết bị hiện đại và đội ngũ bác sĩ giàu kinh nghiệm
              </p>

              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-teal-500 rounded-full mt-2 flex-shrink-0"></div>
                  <p className="text-gray-600">Đội ngũ bác sĩ chuyên môn cao, giàu kinh nghiệm</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-teal-500 rounded-full mt-2 flex-shrink-0"></div>
                  <p className="text-gray-600">Trang thiết bị hiện đại, công nghệ tiên tiến</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-teal-500 rounded-full mt-2 flex-shrink-0"></div>
                  <p className="text-gray-600">Không gian thoải mái, dịch vụ tận tâm</p>
                </div>
              </div>

              <div className="mt-8">
                <button className="bg-teal-500 text-white px-6 py-3 rounded-lg hover:bg-teal-600 transition-colors duration-300 font-medium">
                  Đặt lịch tư vấn ngay
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </section>
  )
}
