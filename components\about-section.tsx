"use client"
import Image from "next/image"
import { useTranslations } from 'next-intl'

export default function AboutSection() {
  const t = useTranslations('about')
  const tDept = useTranslations('departments')

  const departments = [
    {
      key: "examination",
      name: tDept('examination.name'),
      img: "/service/kham-rang.jpg",
      bgColor: "bg-teal-500",
      imageSeaction: "/home/<USER>",
      desc1: tDept('examination.desc1'),
      desc2: tDept('examination.desc2'),
    },
    {
      key: "scaling",
      name: tDept('scaling.name'),
      img: "/service/lay-voi.png",
      bgColor: "bg-red-100",
      imageSeaction: "/home/<USER>",
      desc1: tDept('scaling.desc1'),
      desc2: tDept('scaling.desc2'),
    },
    {
      key: "whitening",
      name: tDept('whitening.name'),
      img: "/service/kham-rang.jpg",
      bgColor: "bg-green-100",
      imageSeaction: "/home/<USER>",
      desc1: tDept('whitening.desc1'),
      desc2: tDept('whitening.desc2'),
    },
    {
      key: "braces",
      name: tDept('braces.name'),
      img: "/service/kham-rang.jpg",
      bgColor: "bg-blue-100",
      imageSeaction: "/home/<USER>",
      desc1: tDept('braces.desc1'),
      desc2: tDept('braces.desc2'),
    },
    {
      key: "crown",
      name: tDept('crown.name'),
      img: "/service/kham-rang.jpg",
      bgColor: "bg-yellow-100",
      imageSeaction: "/home/<USER>",
      desc1: tDept('crown.desc1'),
      desc2: tDept('crown.desc2'),
    },
    {
      key: "implant",
      name: tDept('implant.name'),
      img: "/service/kham-rang.jpg",
      bgColor: "bg-purple-100",
      imageSeaction: "/home/<USER>",
      desc1: tDept('implant.desc1'),
      desc2: tDept('implant.desc2'),
    },
    {
      key: "pediatric",
      name: tDept('pediatric.name'),
      img: "/service/kham-rang.jpg",
      bgColor: "bg-purple-100",
      imageSeaction: "/home/<USER>",
      desc1: tDept('pediatric.desc1'),
      desc2: tDept('pediatric.desc2'),
    },
  ]

  return (
    <section className="py-20" id="about">
      <div className="container mx-auto px-4 max-w-[1124px]">
        <div className="text-center">
          <h2 className="text-3xl font-bold mb-4">{t('whoAreWe')}</h2>
          <div className="flex items-center justify-center mb-6">
            <div className="h-px bg-teal-500 w-16"></div>
            <div className="w-3 h-3 bg-teal-500 rounded-full mx-3 flex items-center justify-center">
              <div className="w-1 h-1 bg-white rounded-full"></div>
            </div>
            <div className="h-px bg-teal-500 w-16"></div>
          </div>
          <div className="max-w-2xl mx-auto">
            <p className="text-gray-600 mb-12">
              {t('whoDescription')}
            </p>
          </div>
        </div>
        <div className="grid lg:grid-cols-2 gap-12 items-center my-20">
          <div>
            <h2 className="text-3xl font-bold mb-6">
              {t('title')}
            </h2>
            <p className="text-gray-600 mb-6 leading-relaxed">
              {t('description1')}
            </p>
          </div>

          <div className="relative">
            <div className="absolute -bottom-6 -left-6 w-full h-full bg-cyan-100 rounded-lg -z-10" />
              <div className="relative rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
                <Image
                  src="/gallery/13.jpg"
                  alt="Bác sĩ tư vấn khách hàng"
                  width={250}
                  height={250}
                  className="object-cover w-full h-full"
                />
              </div>
          </div>
        </div>
      </div>
    </section>
  )
}
