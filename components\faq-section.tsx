"use client"

import { useState } from "react"
import { ChevronDown, ChevronUp } from "lucide-react"
import { Collapsible, CollapsibleTrigger } from "@/components/ui/collapsible"
import Image from "next/image"
import { motion, AnimatePresence } from "framer-motion"
import { useTranslations } from "next-intl"

export default function FaqSection() {
  const [openIndex, setOpenIndex] = useState<number | null>(0)
  const t = useTranslations('faq')
  const faqItems = t.raw('items') as { question: string; answer: string }[]

  const toggleItem = (index: number) => {
    setOpenIndex((prev) => (prev === index ? null : index))
  }

  return (
    <section className="py-20 bg-[url(https://html.laralink.com/nischinto/nischinto/assets/img/shape/faq-bg.svg)] bg-cover bg-center bg-no-repeat">
      <div className="container mx-auto px-4 max-w-[1124px]">
        <div className="grid lg:grid-cols-2 gap-12 items-start">
          <div className="relative flex items-center justify-end">
            <Image
              src="/faq/FAQ.png"
              alt="Medical care"
              width={500}
              height={500}
              className="object-cover"
            />
          </div>

          <div>
            <h2 className="text-3xl font-bold mb-8">{t('title')}</h2>
            <div className="space-y-4">
              {faqItems.map((faq, index) => (
                <Collapsible
                  key={index}
                  open={openIndex === index}
                  onOpenChange={() => toggleItem(index)}
                >
                  <CollapsibleTrigger className="w-full">
                    <div
                      className={`p-4 transition-colors flex items-center justify-between border-x-2 border-t-2 ${
                        openIndex === index
                          ? "bg-teal-500 text-white border-teal-500 border-b-0 rounded-t-lg"
                          : "bg-white text-gray-800 border-gray-200 hover:border-teal-500 rounded-lg border-b-2"
                      }`}
                    >
                      <span className="font-medium text-left">{faq.question}</span>
                      {openIndex === index ? (
                        <ChevronUp className="w-5 h-5" />
                      ) : (
                        <ChevronDown className="w-5 h-5" />
                      )}
                    </div>
                  </CollapsibleTrigger>
                  <AnimatePresence initial={false}>
                    {openIndex === index && (
                      <motion.div
                        key="content"
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: "auto", opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.35, ease: [0.4, 0, 0.2, 1] }}
                        style={{ overflow: "hidden" }}
                      >
                        <div className="p-4 bg-white border-2 border-t-0 border-teal-500 rounded-b-lg">
                          <p className="leading-relaxed">{faq.answer}</p>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </Collapsible>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
