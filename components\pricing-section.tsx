import { But<PERSON> } from "@/components/ui/button"
import { Check, X } from "lucide-react"
import { useTranslations } from "next-intl"

export default function PricingSection() {
  const t = useTranslations("pricing")
  const plans = t.raw("plans") as {
    name: string
    price: string
    features: { name: string; included: boolean }[]
  }[]

  return (
    <section className="py-20 bg-gray-50" id="pricing">
      <div className="container mx-auto px-4 max-w-[1124px]">
        <div className="text-center">
          <h2 className="text-3xl font-bold mb-4">{t("title")}</h2>
          <div className="flex items-center justify-center mb-6">
            <div className="h-px bg-teal-500 w-16"></div>
            <div className="w-3 h-3 bg-teal-500 rounded-full mx-3 flex items-center justify-center">
              <div className="w-1 h-1 bg-white rounded-full"></div>
            </div>
            <div className="h-px bg-teal-500 w-16"></div>
          </div>
        </div>
        <div className="grid md:grid-cols-3 gap-8">
          {plans.map((plan, index) => (
            <div key={index} className="bg-white rounded-lg shadow-lg overflow-hidden">
              <div className="relative text-white text-center rounded-t-lg overflow-hidden">
                <div className="text-2xl font-bold pt-8 pb-20 relative z-10">{plan.price}</div>
                <img
                  src="https://html.laralink.com/nischinto/nischinto/assets/img/shape/price-shape.svg"
                  alt="Wave Shape"
                  className="absolute bottom-0 left-0 w-full"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-800 text-center mb-6">{plan.name}</h3>

                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature, i) => (
                    <li key={i} className="flex items-center">
                      {feature.included ? (
                        <Check className="w-5 h-5 text-green-500 mr-3" />
                      ) : (
                        <X className="w-5 h-5 text-red-500 mr-3" />
                      )}
                      <span className={feature.included ? "text-gray-700" : "text-gray-400"}>
                        {feature.name}
                      </span>
                    </li>
                  ))}
                </ul>
                <Button
                  variant="outline"
                  className="w-full border-teal-500 text-teal-500 hover:bg-teal-500 hover:text-white rounded-full"
                >
                  {t("contact_button")}
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
