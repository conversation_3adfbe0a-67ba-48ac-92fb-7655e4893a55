import { NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { name, email, phone, date, service, message, time, subject } = body;

    // Create a transporter
    const transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: "<EMAIL>",
        pass: "tmflitduilvsjops",
      },
    });

    // Format date for better display
    const formatDate = (dateString: string) => {
      if (!dateString) return 'Not specified';
      const date = new Date(dateString);
      return date.toLocaleDateString('vi-VN', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    };

    const formatTime = (timeString: string) => {
      if (!timeString) return 'Not specified';
      return timeString;
    };
    const serviceNames = {
      examination: 'Khám tổng quát',
      scaling: '<PERSON><PERSON><PERSON> vôi răng',
      whitening: 'T<PERSON><PERSON> trắng răng',
      braces: '<PERSON><PERSON><PERSON> răng',
      crown: '<PERSON><PERSON><PERSON> sứ',
      implant: '<PERSON><PERSON>y ghép Implant',
      contact_inquiry: 'Liên hệ thông tin'
    };

    const serviceName = serviceNames[service as keyof typeof serviceNames] || service || 'Not specified';

    // Enhanced email template
    const emailTemplate = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>New Appointment Request</title>
      <style>
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          line-height: 1.6;
          color: #333;
          background-color: #f8f9fa;
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
          background-color: #ffffff;
          border-radius: 12px;
          overflow: hidden;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        .header {
          background: linear-gradient(135deg, #14b8a6 0%, #0d9488 100%);
          color: white;
          padding: 30px 20px;
          text-align: center;
        }
        .header h1 {
          font-size: 24px;
          font-weight: 600;
          margin-bottom: 8px;
        }
        .header p {
          font-size: 16px;
          opacity: 0.9;
        }
        .content {
          padding: 30px 20px;
        }
        .appointment-info {
          background-color: #f8fafc;
          border-radius: 8px;
          padding: 20px;
          margin-bottom: 25px;
          border-left: 4px solid #14b8a6;
        }
        .info-row {
          display: flex;
          margin-bottom: 15px;
          align-items: flex-start;
        }
        .info-row:last-child {
          margin-bottom: 0;
        }
        .info-label {
          font-weight: 600;
          color: #374151;
          min-width: 100px;
          margin-right: 15px;
        }
        .info-value {
          color: #1f2937;
          flex: 1;
        }
        .highlight-box {
          background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
          border-radius: 8px;
          padding: 20px;
          margin: 20px 0;
          border: 1px solid #93c5fd;
        }
        .highlight-box h3 {
          color: #1e40af;
          font-size: 18px;
          margin-bottom: 10px;
        }
        .appointment-details {
          display: flex;
          gap: 40px;
          margin: 15px 0;
        }
        .detail-item {
          flex: 1;
          text-align: center;
          background-color: white;
          padding: 15px;
          border-radius: 6px;
          border: 1px solid #e5e7eb;
        }
        .detail-item .icon {
          font-size: 24px;
          margin-bottom: 8px;
        }
        .detail-item .label {
          font-size: 12px;
          color: #6b7280;
          text-transform: uppercase;
          font-weight: 600;
          margin-bottom: 5px;
        }
        .detail-item .value {
          font-size: 14px;
          font-weight: 600;
          color: #1f2937;
        }
        .message-section {
          background-color: #fffbeb;
          border-radius: 8px;
          padding: 20px;
          margin: 20px 0;
          border: 1px solid #fbbf24;
        }
        .message-section h4 {
          color: #92400e;
          margin-bottom: 10px;
          font-size: 16px;
        }
        .message-text {
          color: #451a03;
          font-style: italic;
          line-height: 1.5;
        }
        .footer {
          background-color: #f3f4f6;
          padding: 20px;
          text-align: center;
          color: #6b7280;
          font-size: 14px;
        }
        .status-badge {
          display: inline-block;
          background-color: #fef3c7;
          color: #92400e;
          padding: 6px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 600;
          text-transform: uppercase;
          margin-bottom: 20px;
        }
        @media (max-width: 600px) {
          .appointment-details {
            flex-direction: column;
            gap: 10px;
          }
          .info-row {
            flex-direction: column;
          }
          .info-label {
            margin-bottom: 5px;
            min-width: auto;
          }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🦷 SGI Dental Clinic</h1>
          <p>Yêu cầu đặt lịch hẹn mới đã được nhận</p>
        </div>
        <div class="content">
          <div class="highlight-box">
            <h3>📅 Tóm tắt cuộc hẹn</h3>
            <table width="100%" cellpadding="0" cellspacing="0" style="margin: 15px 0;">
            <tr>
              <td style="padding: 15px; text-align: center;">
                <div style="font-size: 24px;">📅</div>
                <div style="font-size: 12px; color: #6b7280; text-transform: uppercase; font-weight: 600; margin-bottom: 5px;">Ngày Đặt</div>
                <div style="font-size: 14px; font-weight: 600; color: #1f2937;">${formatDate(date)}</div>
              </td>
              <td style="padding: 15px; text-align: center;">
                <div style="font-size: 24px;">⏰</div>
                <div style="font-size: 12px; color: #6b7280; text-transform: uppercase; font-weight: 600; margin-bottom: 5px;">Thời Gian</div>
                <div style="font-size: 14px; font-weight: 600; color: #1f2937;">${formatTime(time)}</div>
              </td>
              <td style="padding: 15px; text-align: center;">
                <div style="font-size: 24px;">🔧</div>
                <div style="font-size: 12px; color: #6b7280; text-transform: uppercase; font-weight: 600; margin-bottom: 5px;">Dịch Vụ</div>
                <div style="font-size: 14px; font-weight: 600; color: #1f2937;">${serviceName}</div>
              </td>
            </tr>
            </table>
          </div>
          <div class="appointment-info">
            <h3 style="color: #14b8a6; margin-bottom: 20px; font-size: 18px;">👤 Thông Tin Khách Hàng</h3>
            <div class="info-row">
              <span class="info-label">Tên Khách Hàng:</span>
              <span class="info-value">${name || 'Not provided'}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Email:</span>
              <span class="info-value">${email || 'Not provided'}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Số Điện Thoại:</span>
              <span class="info-value">${phone || 'Not provided'}</span>
            </div>
          </div>

          ${message ? `
          <div class="message-section">
            <h4>💬 Ghi chú của khách hàng</h4>
            <div class="message-text">"${message}"</div>
          </div>
          ` : ''}
        </div>
      </div>
    </body>
    </html>
    `;

    // Email options
    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: '<EMAIL>',
      subject: subject ? `📧 ${subject} - ${name}` : `🦷 New Appointment Request - ${name} (${formatDate(date)} at ${formatTime(time)})`,
      html: emailTemplate,
    };

    await transporter.sendMail(mailOptions);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error sending email:', error);
    return NextResponse.json(
      { error: 'Failed to send email' },
      { status: 500 }
    );
  }
}