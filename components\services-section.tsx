"use client"
import { User<PERSON><PERSON><PERSON>, MessageCircle, AlarmClock } from "lucide-react"
import { useTranslations } from 'next-intl'

export default function ServicesSection() {
  const t = useTranslations('services')

  const services = [
    {
      icon: UserCheck,
      title: t('expertTeam.title'),
      description: t('expertTeam.description'),
      bgColor: "bg-purple-100",
      iconColor: "text-purple-600",
    },
    {
      icon: MessageCircle,
      title: t('flexibleSupport.title'),
      description: t('flexibleSupport.description'),
      bgColor: "bg-green-100",
      iconColor: "text-green-600",
    },
    {
      icon: AlarmClock,
      title: t('emergencySupport.title'),
      description: t('emergencySupport.description'),
      bgColor: "bg-red-100",
      iconColor: "text-red-600",
    },
  ]

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4 max-w-[1124px]">
        <div className="grid md:grid-cols-3 gap-8 mb-20">
          {services.map((service, index) => (
            <div key={index} className="flex flex-col md:items-start items-center md:justify-start justify-center">
              <div
                className={`w-16 h-16 ${service.bgColor} rounded-full flex items-center justify-center mb-6`}
              >
                <service.icon className={`w-8 h-8 ${service.iconColor}`} />
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-4">{service.title}</h3>
              <p className="leading-relaxed text-center md:text-start">{service.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
