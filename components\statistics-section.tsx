"use client"

import { useEffect, useState } from "react"
import Image from "next/image"
import { useTranslations } from "next-intl"
import { User, Clock, Users, Building } from "lucide-react"

export default function StatisticsSection() {
  const t = useTranslations("statistics")

  const stats = [
    {
      icon: User,
      number: 10,
      label: t("0.label"),
      bgColor: "bg-purple-100",
      iconColor: "text-purple-600",
    },
    {
      icon: Clock,
      number: 3000,
      label: t("1.label"),
      bgColor: "bg-green-100",
      iconColor: "text-green-600",
    },
    {
      icon: Users,
      number: 2,
      label: t("2.label"),
      bgColor: "bg-yellow-100",
      iconColor: "text-yellow-600",
    },
    {
      icon: Building,
      number: 3,
      label: t("3.label"),
      bgColor: "bg-blue-100",
      iconColor: "text-blue-600",
    },
  ]

  // Counter hook logic
  function Counter({ target, duration = 2000 }: { target: number; duration?: number }) {
    const [count, setCount] = useState(0)

    useEffect(() => {
      let start = 0
      const stepTime = 16
      const increment = target / (duration / stepTime)

      const interval = setInterval(() => {
        start += increment
        if (start >= target) {
          setCount(target)
          clearInterval(interval)
        } else {
          setCount(Math.ceil(start))
        }
      }, stepTime)

      return () => clearInterval(interval)
    }, [target, duration])

    return <span>{count}</span>
  }

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4 max-w-[1124px]">
        <div className="grid lg:grid-cols-2 gap-12 items-center mb-20">
          {/* Stats grid */}
          <div className="grid grid-cols-2 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center p-6 bg-white rounded-lg shadow-lg">
                <div
                  className={`w-16 h-16 ${stat.bgColor} rounded-full flex items-center justify-center mx-auto mb-4`}
                >
                  <stat.icon className={`w-8 h-8 ${stat.iconColor}`} />
                </div>
                <h3 className="text-3xl font-bold text-gray-800 mb-2">
                  <Counter target={Number(stat.number)} />+
                </h3>
                <p className="text-gray-600">{stat.label}</p>
              </div>
            ))}
          </div>

          <div className="relative">
            <Image
              src="/location/5.jpg"
              alt="Medical professional"
              width={500}
              height={400}
              className="relative z-10  rounded-l-[500px] rounded-br-[500px]"
            />
          </div>
        </div>
      </div>
    </section>
  )
}
