"use client"

import Image from "next/image"
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselPrevious,
  CarouselNext,
} from "@/components/ui/carousel"
import { useTranslations } from "next-intl"

export default function TestimonialsSection() {
  const t = useTranslations('testimonials')

  const testimonials = [
    {
      name: t("0.name"),
      image: "/avatar/chi-b.jpg",
      text: t("0.text"),
    },
    {
      name: t("1.name"),
      image: "/avatar/anh-khanh.PNG",
      text: t("1.text"),
    },
    {
      name: t("2.name"),
      image: "/avatar/chi-a.jpg",
      text: t("2.text"),
    },
    {
      name: t("0.name"),
      image: "/avatar/chi-b.jpg",
      text: t("0.text"),
    },
    {
      name: t("1.name"),
      image: "/avatar/anh-khanh.PNG",
      text: t("1.text"),
    },
    {
      name: t("2.name"),
      image: "/avatar/chi-a.jpg",
      text: t("2.text"),
    },
  
  ]

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4 max-w-[1124px]">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">{t("title")}</h2>
          <div className="flex items-center justify-center mb-6">
            <div className="h-px bg-teal-500 w-16"></div>
            <div className="w-3 h-3 bg-teal-500 rounded-full mx-3 flex items-center justify-center">
              <div className="w-1 h-1 bg-white rounded-full"></div>
            </div>
            <div className="h-px bg-teal-500 w-16"></div>
          </div>
          <p className="text-gray-600">{t("description1")}</p>
          <p className="text-gray-600">{t("description2")}</p>
        </div>

        <div className="relative group">
          <Carousel className="w-full max-w-4xl mx-auto" opts={{ loop: true }}>
            <CarouselContent>
              {testimonials.map((testimonial, index) => (
                <CarouselItem key={index} className="md:basis-1/3">
                  <div className="text-center px-4">
                    <div className="w-20 h-20 rounded-full overflow-hidden mx-auto mb-6">
                      <Image
                        src={testimonial.image}
                        alt={testimonial.name}
                        width={80}
                        height={80}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <h3 className="text-xl font-bold text-gray-800 mb-2">{testimonial.name}</h3>
                    <p className="text-gray-600 leading-relaxed">{testimonial.text}</p>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
          </Carousel>
        </div>
      </div>
    </section>
  )
}
