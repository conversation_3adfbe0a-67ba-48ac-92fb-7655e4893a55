import { getRequestConfig } from 'next-intl/server';

export const locales = ['vi', 'en'];

export default getRequestConfig(async ({ locale }) => {
  // Default to 'vi' if locale is not provided
  if (!locale || !locales.includes(locale)) {
    locale = 'vi';
  }

  console.log('Loading messages for locale:', locale);

  return {
    locale,
    messages: (await import(`./messages/${locale}.json`)).default
  };
});
